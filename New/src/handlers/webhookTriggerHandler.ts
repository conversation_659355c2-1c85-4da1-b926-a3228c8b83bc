/**
 * Webhook Trigger Handler
 *
 * Processes the oldest pending webhook from the webhook_queue table with
 * automatic sequential processing and concurrency control. This handler implements
 * a robust sequential webhook processing system that:
 * 0. Checks for active processing to prevent concurrent execution
 * 1. Queries for the oldest pending webhook using two-tier strategy:
 *    - First Priority: Fresh pending webhooks (retryCount = 0)
 *    - Second Priority: Retry-eligible webhooks (retryCount > 0 and < 4)
 * 2. Updates its status to "processing"
 * 3. Determines the appropriate internal webhook URL based on source
 * 4. Sends HTTP POST request to the internal webhook endpoint
 * 5. Updates webhook status based on response (completed/failed)
 * 6. Automatically triggers next pending webhook if available (sequential chain)
 * 7. Returns response for current webhook
 *
 * Key Features:
 * - Concurrency control: Only one processing chain runs at a time
 * - Sequential processing: Automatic chaining until queue is empty
 * - Thread-safe operations: Prevents race conditions and duplicate processing
 * - Comprehensive logging: Tracks processing state and active webhooks
 * - Graceful error handling: Maintains system stability
 *
 * @fileoverview Webhook trigger handler with concurrency control and sequential processing
 * @version 1.2.0
 * @since 2024-08-05
 */

import { dbSchema, getDb } from "@database";
import { and, asc, eq, gt, lt } from "drizzle-orm";
import type { Context } from "hono";
import { Process } from "@/processors/process";
import { fireAndForgetWebhookTrigger } from "@/utils";
import { logWebhookError } from "@/utils/errorLogger";
import { logError, logInfo } from "@/utils/logger";

/**
 * Trigger next pending webhook in the processing chain
 *
 * After successfully processing a webhook, this function checks if there are
 * additional pending webhooks in the queue and automatically triggers the next
 * webhook processing request. This creates a sequential processing chain that
 * continues until all pending webhooks are processed.
 *
 * Features:
 * - Checks for next pending webhook in chronological order
 * - Adds rate limiting delay to prevent system overload
 * - Graceful error handling that doesn't affect current webhook response
 * - Comprehensive logging for chain tracking
 * - Fire-and-forget execution to maintain response performance
 *
 * @param baseUrl - Base URL for constructing the webhook trigger endpoint
 * @param currentWebhookId - ID of the webhook that was just processed
 * @returns Promise that resolves when next webhook is triggered or no more pending
 *
 * @since 1.0.0
 */
async function triggerNextWebhookIfPending(): Promise<void> {
	try {
		const db = getDb();

		// Check for next pending webhook using two-tier strategy (same logic as main handler)
		// First Priority: Look for fresh pending webhooks (retryCount = 0)
		let pendingWebhooks = await db
			.select()
			.from(dbSchema.webhookQueue)
			.where(
				and(
					eq(dbSchema.webhookQueue.status, "pending"),
					eq(dbSchema.webhookQueue.retryCount, 0),
				),
			)
			.orderBy(asc(dbSchema.webhookQueue.createdAt))
			.limit(1);

		// Second Priority: If no fresh webhooks, look for retry-eligible webhooks
		if (pendingWebhooks.length === 0) {
			pendingWebhooks = await db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						eq(dbSchema.webhookQueue.status, "pending"),
						gt(dbSchema.webhookQueue.retryCount, 0),
						lt(dbSchema.webhookQueue.retryCount, 4),
					),
				)
				.orderBy(asc(dbSchema.webhookQueue.createdAt))
				.limit(1);
		}

		if (pendingWebhooks.length === 0) {
			logInfo(`Webhook processing chain completed. No more pending webhooks.`);
			return;
		}

		const nextWebhook = pendingWebhooks[0];
		logInfo(
			`Continuing webhook processing chain: triggering next webhook ${nextWebhook.id} (source: ${nextWebhook.source}).`,
		);
		// Trigger next webhook processing
		fireAndForgetWebhookTrigger("/webhook-trigger", {
			webhookId: nextWebhook.id,
			source: nextWebhook.source,
			chainContinuation: true,
		});
	} catch (error) {
		logError(`Error in webhook processing chain continuation.`, error);
	}
}

/**
 * Webhook trigger handler with concurrency control and automatic sequential processing
 *
 * Processes the oldest pending webhook from the queue with robust concurrency control
 * and automatically triggers processing of subsequent pending webhooks in a sequential chain.
 * This eliminates the need for manual triggers while preventing concurrent processing.
 *
 * Processing steps:
 * - Checking for active processing to prevent concurrent execution
 * - Finding the oldest pending webhook using two-tier strategy:
 *   * First Priority: Fresh pending webhooks (retryCount = 0)
 *   * Second Priority: Retry-eligible webhooks (retryCount > 0 and < 4)
 * - Updating its status to "processing"
 * - Determining the appropriate internal webhook URL based on source
 * - Making HTTP POST request to internal webhook URL with payload
 * - Updating status based on response (completed/failed)
 * - Automatically triggering next pending webhook if available
 * - Returning response for current webhook (chain continues in background)
 *
 * Concurrency control features:
 * - Database-backed processing state tracking
 * - Thread-safe operations to prevent race conditions
 * - Graceful handling when processing is already active
 * - Comprehensive logging of processing state and conflicts
 *
 * Sequential processing features:
 * - Rate limiting delay (1.5s) between webhook triggers
 * - Graceful error handling that doesn't break the chain
 * - Fire-and-forget execution to maintain response performance
 * - Automatic chain continuation until queue is empty
 *
 * @param c - Hono context object
 * @returns Promise resolving to HTTP Response for the current webhook
 */
export async function webhookTriggerHandler(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	const db = getDb();

	// Step 1: Find the oldest pending webhook using two-tier strategy
	// First Priority: Look for fresh pending webhooks (retryCount = 0)
	let pendingWebhooks = await db
		.select()
		.from(dbSchema.webhookQueue)
		.where(
			and(
				eq(dbSchema.webhookQueue.status, "pending"),
				eq(dbSchema.webhookQueue.retryCount, 0),
			),
		)
		.orderBy(asc(dbSchema.webhookQueue.createdAt));

	// Second Priority: If no fresh webhooks, look for retry-eligible webhooks
	if (pendingWebhooks.length === 0) {
		pendingWebhooks = await db
			.select()
			.from(dbSchema.webhookQueue)
			.where(
				and(
					eq(dbSchema.webhookQueue.status, "pending"),
					gt(dbSchema.webhookQueue.retryCount, 0),
					lt(dbSchema.webhookQueue.retryCount, 4),
				),
			)
			.orderBy(asc(dbSchema.webhookQueue.createdAt));
	}
	const baseUrl = new URL(c.req.url).origin;

	let res: Response;

	try {
		logInfo("Webhook trigger endpoint called - checking for active processing");

		// Step 0: Check if webhook processing is already active
		const queueState = await Process.isQueueProcessing();
		if (queueState.isProcessing) {
			return c.json(
				{
					status: "info",
					message: "Webhook processing already in progress",
					metadata: {
						timestamp,
						activeProcessing: {
							webhookId: queueState.activeWebhookId,
							source: queueState.activeWebhookSource,
							sourceId: queueState.activeWebhookSourceId,
							startedAt: queueState.processingStartedAt,
						},
					},
				},
				200,
			);
		}

		if (pendingWebhooks.length < 1) {
			res = c.json(
				{
					status: "success",
					message: "Relax dude, We got this",
					metadata: {
						timestamp,
					},
				},
				404,
			);
		}

		const webhook = pendingWebhooks[0];

		// Step 2: Update webhook status to "processing"
		const processingStartedAt = new Date();
		await db
			.update(dbSchema.webhookQueue)
			.set({
				status: "processing",
				processingStartedAt,
				updatedAt: processingStartedAt,
			})
			.where(eq(dbSchema.webhookQueue.id, webhook.id));
		// Step 3: Determine internal webhook URL based on source

		let internalWebhookUrl: string;

		switch (webhook.source) {
			case "cc":
				internalWebhookUrl = `${baseUrl}/internal-webhook/cc`;
				break;
			case "ap":
				internalWebhookUrl = `${baseUrl}/internal-webhook/ap`;
				break;
			default:
				throw new Error(`Unknown webhook source: ${webhook.source}`);
		}

		// Step 4: Send HTTP POST request to internal webhook URL
		let response: Response;
		let processingResult: "processing" | "failed";
		let errorMessage: string | null = null;
		let errorDetails: Record<string, unknown> | null = null;

		try {
			response = await fetch(internalWebhookUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "WebhookTrigger/1.0",
				},
				body: JSON.stringify(webhook.payload),
			});

			if (response.ok) {
				// Keep it processing - admin endpoint will mark as completed
				processingResult = "processing";
				logInfo(`Webhook ${webhook.id} internal processing successful, keeping in processing state`);
			} else {
				processingResult = "failed";
				const errorText = await response.text();
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
				errorDetails = {
					status: response.status,
					statusText: response.statusText,
					responseBody: errorText,
				};
				logError(`Webhook ${webhook.id} processing failed: ${errorMessage}`);
			}
		} catch (fetchError) {
			processingResult = "failed";
			errorMessage = `Network error: ${
				fetchError instanceof Error ? fetchError.message : String(fetchError)
			}`;
			errorDetails = {
				error:
					fetchError instanceof Error ? fetchError.stack : String(fetchError),
				url: internalWebhookUrl,
			};
			logError(`Webhook ${webhook.id} network error: ${errorMessage}`);
		}

		// Step 5: Update webhook status based on response
		const now = new Date();
		const updateData: Record<string, unknown> = {
			status: processingResult,
			updatedAt: now,
		};

		if (processingResult === "failed") {
			updateData.processingCompletedAt = now;
			updateData.errorMessage = errorMessage;
			updateData.errorDetails = errorDetails;
		}
		// If processing, don't set processingCompletedAt - it's still processing

		await db
			.update(dbSchema.webhookQueue)
			.set(updateData)
			.where(eq(dbSchema.webhookQueue.id, webhook.id));

		// Log webhook error if processing failed
		if (processingResult === "failed") {
			await logWebhookError(
				new Error(errorMessage || "Unknown webhook processing error"),
				{
					webhookId: webhook.id,
					source: webhook.source,
					sourceId: webhook.sourceId,
					internalUrl: internalWebhookUrl,
					errorDetails,
				},
			);
		}

		// Step 7: Return appropriate response
		const processingDuration =
			now.getTime() - processingStartedAt.getTime();

		if (processingResult === "processing") {
			res = c.json(
				{
					status: "success",
					message: "Webhook internal processing successful, awaiting completion",
					metadata: {
						timestamp,
						webhookId: webhook.id,
						source: webhook.source,
						sourceId: webhook.sourceId,
						processingDurationMs: processingDuration,
					},
				},
				200,
			);
		} else {
			res = c.json(
				{
					status: "error",
					message: "Webhook processing failed",
					metadata: {
						timestamp,
						webhookId: webhook.id,
						source: webhook.source,
						sourceId: webhook.sourceId,
						processingDurationMs: processingDuration,
						errorMessage,
					},
				},
				500,
			);
		}
	} catch (error) {
		logError("Webhook trigger handler error", error);

		await logWebhookError(
			error instanceof Error ? error : new Error(String(error)),
			{
				endpoint: "/webhook-trigger",
				timestamp,
			},
		);

		res = c.json(
			{
				status: "error",
				message: "Internal server error during webhook processing",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}

	// Step 6: Check for and trigger next pending webhook in the processing chain
	// This creates automatic sequential processing of the entire webhook queue
	// Fire-and-forget execution to maintain response performance
	if (pendingWebhooks.length > 1) {
		triggerNextWebhookIfPending();
	}
	return res;
}
